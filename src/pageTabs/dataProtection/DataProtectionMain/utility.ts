import i18n from 'i18next';
import { RULES_MAP, DESENS_BATCH_ACTIONS, DESENS_STATUS, GRADE_DESENS_BATCH_ACTIONS } from '../constant';
import {
  getInternalDatasourceFind,
  getInternalGroupFind,
  getInternalDenseRules,
  updateDatasourceInternal,
  updateGroupInternalStatus,
  updateSchemaInternal
} from 'src/api';

export function generateObjectName(path: string): string {
  try {
    const targetType: string[] = ['DATABASE', 'SCHEMA', 'TABLE']

    const path2Arr = path.split('/').filter((i) => i)
    const result: string[] = []

    path2Arr.forEach((item) => {
      const splitArr = item.split(':')
      if (targetType.includes(splitArr[0])) {
        result.push(splitArr[1])
      }
    })

    return result.length ? result.join('.') : ''
  } catch (error) {
    return ''
  }
}

export function formatRuleName(key: string): string {
  return RULES_MAP[key] ? i18n.t(`dataProtection.dense.denseRule.${key}`)  : ''
}

export const getDesensInternalDataAction = (type: any) => {
  switch (type) {
    case "datasource":
      return getInternalDatasourceFind;
    case "group":
      return getInternalGroupFind;
    default:
      return getInternalDenseRules;
  }
};
export const getDesensInternalStatusAction = (type: any) => {
  switch (type) {
    case "datasource":
      return updateDatasourceInternal;
    case "group":
      return updateGroupInternalStatus;
    default:
      return updateSchemaInternal;
  }
};

export const formatSelectOptions = Object.keys(DESENS_BATCH_ACTIONS).map((key: string) => ({label: DESENS_BATCH_ACTIONS[key], value: key}))
export const formatGradeSelectOptions = Object.keys(GRADE_DESENS_BATCH_ACTIONS).map((key: string) => ({label: GRADE_DESENS_BATCH_ACTIONS[key], value: key}))

export const formatStatusSelectOptions = () =>   Object.keys(DESENS_STATUS).map((key: string) => ({label: i18n.t(`dataProtection.desens.densesStatus.${key}`) , value: key}))

// 窗口期设置所有接口需要传的节点类参数
export const getWindowPeriodParams = (connectionType: any, id: any, nodePathWithType: any, nodeType: any, connectionId: any): any => {
  let params: any = {
    dataSourceType: connectionType,
    nodeType: nodeType,
  }
  // 根据节点类型处理参数
  switch (nodeType) {
    case "datasource":
      params.requestType = "DATASOURCE"
      break;
    case "group":
      params = {
        ...params,
        requestType: "GROUP",
        groupId: Number(id)
      }
      break;
    default:
      params = {
        ...params,
        requestType: "CONNECTION",
        nodePath: nodePathWithType,
        connectionId: connectionId
      }
      break;
  }
  return params
}

// 判断数组中的元素是否为全数字类型
export function isAllNumbers(arr: any[]) {
  if(!arr.length) return false
  return arr.every(item => {
    // 处理原始数字类型（number）
    if (typeof item === 'number') {
      return !Number.isNaN(item); // 排除 NaN（isNaN('1') 返回 false，无需额外处理）
    }
    // 处理字符串类型的数字（如 '1'、'3.14'）
    if (typeof item === 'string') {
      return !Number.isNaN(Number(item)) && item.trim() !== ''; // 排除空字符串、非数字字符串
    }
    // 其他类型（如 boolean、object、null 等）均返回 false
    return false;
  });
}