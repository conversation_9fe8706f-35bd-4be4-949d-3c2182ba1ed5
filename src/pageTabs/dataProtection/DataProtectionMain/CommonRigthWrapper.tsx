import React, { useEffect, useState, useMemo } from "react";
import { useLocation } from 'react-router-dom';
import { useSelector } from "src/hook";
import {
  ViewType,
  getDesensitizeResourcePageList,
  getDesensitizeByGrade
} from "src/api";
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import { RootDesensitize } from "./TableView/RootDesensitize";
import _ from "lodash";

type IType =
  | "connection"
  | "database"
  | "datasource"
  | "group"
  | "schema"
  | "tableGroup";
//除table 层其他页面全部公用 除了接口不一样
const CommonRightWrapper = ({ type }: { type: IType }) => {
  const location = useLocation();
  const { state = {} } = location as { state: GloablSearchLocationState }
  const { selectedNode } = useSelector((state) => state.dataProtection);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false)
  const displayType = useSelector((state)=>state.dataProtection.displayType)
  const currentSelectedGrade = useSelector((state)=>state.dataProtection.currentSelectedGrade)
  const [rootDesensitizeTableParams, setRootDesensitizeTableParams] = useState<{
    search?: string;
    status?: any;
    pageSize?: number;
    current?: number;
  }>({});

  const getActionAndParams = (type: IType) => {
    if (!selectedNode) return {}

    switch (type) {
      case "datasource":
        return {
          params: {
            datasourceType: selectedNode.props?.id,
            ...rootDesensitizeTableParams,
          },
        };

      case "connection":
        const {
          id: connectionId,
          nodePathWithType: nodePath,
          nodeType: findType,
        } = selectedNode.props
        if (!connectionId) return {}
        return {
          params: {
            connectionId: +connectionId as number,
            nodePath,
            findType: findType as ViewType,
            ...rootDesensitizeTableParams,
          },
        };
      case "database":
      case "schema":
      case "tableGroup":
        return {
          params: {
            connectionId: selectedNode.props?.connectionId as number,
            nodePath: selectedNode.props?.nodePathWithType,
            findType: selectedNode.props?.nodeType as ViewType,
            ...rootDesensitizeTableParams,
          },
        };
      case "group":
        return {
          params: {
            groupId: `${selectedNode?.props?.id}`,
            ...rootDesensitizeTableParams,
          },
        };
      default:
        return {}
    }
  };
  //不同类型， 接口不同参数不同
  const fetchList = async () => {
    if (!selectedNode) return
    setLoading(true)
    const { params } = getActionAndParams(type) as any;
    if (params) {
      let data;
      // 分级结果所调用的接口不同
      if(displayType === 'GRADE'){
        let newParams = {
          pageSize: params.pageSize,
          current: params.currentPage,
          dataSourceType: params.dataSourceType || '',
          connectionName: params.connectionName || '',
          columnName:params.columnName || '',
          columnType:params.columnType || '',
          columnComment:params.columnComment || '',
          classifyName:params.classifyName || '',
          labelName:params.labelName || '',
          // 查询全部等级时，levelName传空串
          levelName:currentSelectedGrade?.key === '全部等级'? '' : currentSelectedGrade?.key,
        }
        data = await getDesensitizeByGrade(newParams)
      }else {
        data = await getDesensitizeResourcePageList(params)
      }
      setData(data || {})
    }
    setLoading(false)
  }
  useEffect(() => {
    
    if (Object.keys(rootDesensitizeTableParams)?.length > 0) {
      fetchList()
    }
  }, [selectedNode, type, JSON.stringify(rootDesensitizeTableParams),displayType,currentSelectedGrade])


  //全局搜索
  useEffect(() => {
    
    if (state?.globalSearchRecordPosition && rootDesensitizeTableParams?.current && rootDesensitizeTableParams?.pageSize) {
      //处理分页 并选中
      const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / rootDesensitizeTableParams.pageSize);

      if (rootDesensitizeTableParams.current !== pageNum) {
        setRootDesensitizeTableParams({
          ...rootDesensitizeTableParams,
          current: pageNum
        })
      }
    }
  }, [state?.globalSearchRecordPosition, rootDesensitizeTableParams?.current, rootDesensitizeTableParams?.pageSize])
  //搜索定位
  const isSelectedRowIndex = useMemo(() => {

    if (!state?.globalSearchRecordPosition || !rootDesensitizeTableParams?.current || !rootDesensitizeTableParams?.pageSize) {
      return null;
    }

    const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / rootDesensitizeTableParams.pageSize);
    if (pageNum === rootDesensitizeTableParams.current && state?.object?.datasourceType === selectedNode?.connectionType &&  state?.globalSearchTabKey === 'DESENS') {
      const itemIndexInPage = state.globalSearchRecordPosition % rootDesensitizeTableParams.pageSize;
      return itemIndexInPage === 0 ? rootDesensitizeTableParams.pageSize - 1 : itemIndexInPage - 1;
    }
  }, [rootDesensitizeTableParams?.current, rootDesensitizeTableParams?.pageSize, selectedNode?.id, state?.globalSearchRecordPosition])


  return (
    <RootDesensitize
      loading={loading}
      isSelectedRowIndex={isSelectedRowIndex}
      dataSource={data}
      refresh={() => fetchList()}
      getParams={(params: any) => {
        const cloneParams = _.cloneDeep(params);
        const status = cloneParams?.status?.toLowerCase();
        if (status === "all") {
          delete cloneParams?.status;
        } else {
          cloneParams.status = (status === "true");
        }
        setRootDesensitizeTableParams(cloneParams);
      }}
    />
  );
};

export default CommonRightWrapper;
