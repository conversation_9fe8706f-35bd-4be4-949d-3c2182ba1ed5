import { Button, Divider, Form, Select, message } from 'antd'
import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'
import React, { useEffect, useState , memo, useMemo, useRef} from 'react'
import { UpdateDesensAlgo, getDesensitizeRules, batchUpdateDesensAlgo, getBatchDesensitizeRules, setNonDesensitizeField } from 'src/api'
import { UIModal } from 'src/components'
import { FormLayout } from 'src/constants'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { DesensitizationSettingItems } from './DesensitizationSettingItems'
import { resetDesensRuleSettingObj } from '../../DataProtectionPageSlice'
import PreviewDataTable from './PreviewDataTable'

export const CommonEditDesensRulesModal = memo((): JSX.Element => {
  const [form] = Form.useForm()
  const dispatch = useDispatch()
  const { t } = useTranslation()

  const { selectedNode, desensRuleSettingObj ={} } = useSelector(
    (state) => state.dataProtection,
  )
  const {refresh, editRecord, isScanResult = false} = desensRuleSettingObj;
  const visible = useSelector((state) => state.modal.CommonEditDesensRulesModal?.visible || false);
  
  const [option, setOption] = useState<string>('')
  const [isPreview, setIsPreview] = useState(false)
  const previewDataTableRef = useRef<any>(null)
  const displayType = useSelector((state) => state.dataProtection.displayType);

  const { run: runUpdateRules, loading } = useRequest( (params: any) => {
    if (editRecord?.isBatch ) {
      const ruleParam = {
        max:params.algoParam?.max,
        min:params.algoParam?.min,
      }
      if(displayType === 'GRADE'){
        const newParams = editRecord.recordArr?.map((item:any)=>{
          return {
            nodePath: item.nodePath,
            datasourceType: item.datasourceType,
            columnType: item.columnType,
            ruleName: params.ruleName,
            ruleParam,
          }
        })
        return setNonDesensitizeField(newParams)
      }else {
        const newParams = _.cloneDeep(params) ;
        delete newParams?.id;
        return batchUpdateDesensAlgo({
          ids: params?.id, //脱敏字段 批量设置参数不一致
          ...newParams
        })
      }
    }else{
      return UpdateDesensAlgo(params)
    }
  }, {
    manual: true,
    onSuccess: async () => {
      message.success(`设置成功`)
      dispatch(hideModal('CommonEditDesensRulesModal'))
      refresh()
      form.resetFields()
      dispatch(resetDesensRuleSettingObj());
      setOption('')
    },
  })

  // 脱敏算法
  const { data: selectOptions = [], run: getDenseRules } = useRequest( (params: any) => {
    //批量编辑脱敏字段
    if (editRecord?.isBatch) {
    return getBatchDesensitizeRules({columnTypes: params?.columnType, datasourceType: params?.datasourceType})
   }
   return getDesensitizeRules(params)
  },
    {
      manual: true,
      formatResult: (data) =>
        data.map(
          (item: {
            ruleType: string
            ruleParam: object
            values: string[]
            ruleName: string
          }) => {
            return {
              label: item.ruleName,
              value: item.ruleType,
              props: {
                ruleParam: item.ruleParam,
                options: item.values,
              },
            }
          },
        ),
    },
  )

  useEffect(() => {
    if (selectedNode && visible && editRecord?.columnType) {
      // const { connectionType: datasourceType } = selectedNode.props
      getDenseRules({ columnType: editRecord?.columnType, datasourceType:editRecord.datasourceType})
    }
    if (editRecord) {
      form.setFieldsValue({...editRecord});
        setOption(editRecord?.ruleName || '')
    }
  }, [getDenseRules, selectedNode, visible, editRecord])

  async function submitForm(values: any) {
    const params = {
      ...values,
      
    }

    if (params?.ruleParam?.dateRange) {
      const { dateRange } = values.ruleParam as { dateRange: moment.Moment[] }

      params.ruleParam.min = +dateRange[0].format('x')
      params.ruleParam.max = +dateRange[1].format('x')
      delete params.ruleParam.dateRange
    }
    if(editRecord) {
      params.id = editRecord.id;
    }
   
    if (values?.ruleParam) {
      params.algoParam = values?.ruleParam
      delete params.ruleParam
    }

    runUpdateRules(params)
  }
 
  const getModalTitle = () => {
    if (isScanResult) return t('dataProtection.desens.editScanResult');
    if (editRecord?.isBatch) {
      return t('dataProtection.desens.batchEdit.desensField');
    }
    return t('dataProtection.desens.editDesensField')
  }

  // 脱敏算法设置改变
  const handleValuesChange = (changedValues: any) => {
    setIsPreview((item: boolean) => {
      if (previewDataTableRef?.current) {
        previewDataTableRef?.current?.valuesChange(changedValues);
      }
      return item;
    })
  };

  const handleAfterClose = () => {
    form.resetFields()
    setIsPreview(false)
  }

  // 脱敏算法设置
  const renderDesensitizationSettingItems = useMemo(() => {
    return option.length > 0 && (
      <DesensitizationSettingItems
        option={option}
        form={form}
        //@ts-ignore
        values={{
          ...selectOptions?.find((i: any) => i.value === option)?.props,
          ...(!_.isEmpty(editRecord?.algoParam) && editRecord?.ruleName === option ? { ruleParam: editRecord?.algoParam } : {})
        }}
      />
    )
  }, [editRecord?.algoParam, editRecord?.ruleName, form, option, selectOptions])

  // 脱敏效果预览
  const renderPreviewDataTable = useMemo(() => {
    const { nodePath, nodePathWithType } = editRecord || {};
    return (
      <PreviewDataTable
        ref={previewDataTableRef}
        form={form}
        nodePathWithType={nodePath || nodePathWithType}
      />
    )
  }, [editRecord, form, isScanResult])

  return (
    <UIModal
      title={getModalTitle()}
      visible={visible}
      onOk={() => {
        form.submit()
      }}
      onCancel={() => {
        dispatch(hideModal('CommonEditDesensRulesModal'))
        setOption('')
        dispatch(resetDesensRuleSettingObj());
      }}
      afterClose={handleAfterClose}
      confirmLoading={loading}
    >
      <Form
        form={form}
        onFinish={submitForm}
        {...FormLayout}
        onValuesChange={(changedValues: any) => handleValuesChange(changedValues)}
      >
        <Form.Item
          name="ruleName"
          label={t('dataProtection.desens.desensSource')}
          hidden={editRecord?.isBatch}
        >
         {editRecord?.source}
        </Form.Item>
        <Form.Item 
          name="matchRule" 
          label={t('dataProtection.desens.matchRule')}
          hidden={!isScanResult}
        >
         {editRecord?.matchRule}
        </Form.Item>
        <Form.Item
          name="ruleName"
          label={t('dataProtection.desens.desensAlgorithm')}
          rules={[{ required: true, message: t('dataProtection.desens.desensAlgorithm.tip') }]}
        >
          <Select
            options={selectOptions as any}
            optionFilterProp="label"
            showSearch
            onChange={(v: string) => {
              form.setFieldsValue({ruleName: v})
              setOption(v)
            }}
          ></Select>
        </Form.Item>
        {/* 脱敏算法设置 */}
        <Divider style={{ fontSize: '10px' }}>{t('dataProtection.desens.desensAlgorithm.title')}</Divider>
        {renderDesensitizationSettingItems}
        {/* 脱敏效果预览 */}
        {
          !editRecord?.isBatch && option && option !== "FILTER" && (
            isPreview ? (
              <Form.Item label={t('dataProtection.desens.desensPreview')}>
                {renderPreviewDataTable}
              </Form.Item>
            ) : (
              <Divider style={{ fontSize: '12px' }}>
                {t('dataProtection.desens.desensPreview')}
                <Button
                  type='primary'
                  size='small'
                  style={{ marginLeft: '10px' }}
                  onClick={() => setIsPreview(true)}
                >
                  {t('common.btn.preview')}
                </Button>
              </Divider>
            )
          )
        }
      </Form>
    </UIModal>
  )
})
