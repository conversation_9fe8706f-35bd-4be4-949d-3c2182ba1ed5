import React, { useEffect, useState } from "react";
import { MoreOutlined, PlusSquareOutlined } from '@ant-design/icons'
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { Dropdown, Menu, Modal, Tooltip, Tree, Space, Button, message, Spin } from "antd";
import { Iconfont } from "src/components";
import { useSelector } from "src/hook";
import { NewTemplateOperation } from '../../types';
import { NEW_TEMPLATE_OPERATIONS } from '../../constants';
import { useNewTreeStateManager } from './hooks/useNewTreeStateManager';
import styles from "./index.module.scss";

interface INewTemplateTreeProps {
  needRefreshTree?: boolean;
  clearCacheSignal?: number;
  treeUpdateInfo?: {
    type: 'add' | 'edit' | 'delete' | 'refresh';
    nodeId?: string;
    parentId?: string;
    data?: any;
  } | null;
  onTreeUpdateComplete?: () => void;
  onAddOrEditClass: (node?: any) => void;
  onAddOrEditTemplate: (node?: any) => void;
  onDeleteTreeNode: (params: any) => void;
  onSettingGradeConfg: (key: string) => void;
  onImportTemplate: () => void;
}

const { DirectoryTree } = Tree;

const NewTemplateTree = React.memo((props: INewTemplateTreeProps): JSX.Element => {
  const {
    needRefreshTree,
    clearCacheSignal,
    treeUpdateInfo,
    onTreeUpdateComplete,
    onAddOrEditClass,
    onAddOrEditTemplate,
    onDeleteTreeNode,
    onSettingGradeConfg,
    onImportTemplate
  } = props;

  const { t } = useTranslation();
  const { selectedTemplateNodeInfo } = useSelector((state) => state.dataClassification);
  const [treeHeight, setTreeHeight] = useState<number>(300);

  // 使用状态管理Hook
  const {
    treeData,
    expandedKeys,
    rootLoading,
    setExpandedKeys,
    onLoadData,
    refreshTree,
    clearTpDataCache,
    handleNodeSelect
  } = useNewTreeStateManager(needRefreshTree, treeUpdateInfo, onTreeUpdateComplete);

  const queryTreeHeight = () => {
    const clientHeight = document.documentElement.clientHeight
    const treeHeight = clientHeight > 520 ? clientHeight - 330 : 330
    setTreeHeight(treeHeight)
  }

  useEffect(() => {
    queryTreeHeight();
    window.addEventListener('resize', queryTreeHeight);
    return () => {
      window.removeEventListener('resize', queryTreeHeight);
    }
  }, []);

  // 监听缓存清理信号
  useEffect(() => {
    if (clearCacheSignal && clearCacheSignal > 0) {
      clearTpDataCache(); // 清空所有缓存
    }
  }, [clearCacheSignal, clearTpDataCache]);

  const onDeleteConfirmModal = (node: any) => {
    Modal.confirm({
      centered: true,
      title: t('common.text.delete.tip'),
      onOk: () => {
        //区分删除分类 或删除模板
        let params: any = {
          action: 'delete_template', 
          id: node?.tp_id
        }
        if (!node?.isRoot) { //删除分类
          params = {
            label: node?.title,  //不确定
            id: node?.key,
          }
        }
        onDeleteTreeNode({});
      },
    });
  }

  const onHandleMoreOperations = (key: NewTemplateOperation, node?: any) => {
    switch (key) {
      case 'addClass':
        onAddOrEditClass();
        break;
      case 'addTemplate':
        onAddOrEditTemplate();
        break;
      case 'delete':
        //模板可直接删除
        if (node?.nodeType === 'datasource') {
          onDeleteConfirmModal(node);
          return;
        }
        
        //存在下层节点
        if (node?.children?.length) {
          return message.error(t('classGrading.tab.template.delete.tip'));
        } else {
          //已关联标签 不允许删除
          if (node?.isBind) {
            return message.error(t('classGrading.tab.template.delete.tip2'));
          }
          //最下层节点 且 未关联标签 允许删除
          onDeleteConfirmModal(node);
        }

        break;
      case 'gradeConfig':
        onSettingGradeConfg(node)
        break;
      case 'importTemplate':
        onImportTemplate();
        break;
      case 'edit':
        if (node?.isRoot) {
          // 编辑模板
          onAddOrEditTemplate(node)
        } else {
          // 编辑分类
          onAddOrEditClass(node)
        }
        break;
      default:
        break;
    }
  }

  const treeMenu = (node: any) => {
    const { isRoot = false } = node;
    let operations = [];

    if (isRoot) {
      // 根节点（模板）：新增模板、新增分类、编辑、删除
      operations = ['addTemplate', 'addClass', 'edit', 'delete'];
    } else {
      // 分类节点的基础操作
      operations = ['edit', 'delete'];

      // 等级配置：只有最底层的分类有此操作（没有子节点）
      if (!node?.children?.length) {
        operations.unshift('gradeConfig');
      }

      // 新增分类：只有当此分类下未关联任何标签时才有
      if (!node?.isBind) {
        operations.unshift('addClass');
      }
    }

    return (
      <Menu
        className={styles.optionTxt}
        onClick={({ key }) => { onHandleMoreOperations(key as NewTemplateOperation, node) }}
      >
        {
          operations.map(operation => (
            <Menu.Item key={operation}>{NEW_TEMPLATE_OPERATIONS[operation as NewTemplateOperation]}</Menu.Item>
          ))
        }
      </Menu>
    )
  }

  const titleRender = (node: any) => {
    return (
      <div className={styles.treeTitleItem}>
        <div className={styles.titleTxtWrap}>
          <span className={styles.titleTxt}>{node?.title}</span>
        </div>
     
        <Dropdown overlay={() => treeMenu(node)}>
          <MoreOutlined className={classNames(styles.ml10, styles.options)} />
        </Dropdown>
      </div>
    )
  }

  return (
    <Spin spinning={rootLoading}>
      <Space className="flexAlignCenterJustifyEnd">
        <Tooltip title={t('classGrading.tab.template.tab.new')}>
          <Button
            type='link'
            icon={<PlusSquareOutlined />}
            onClick={() => { onHandleMoreOperations('addTemplate') }}
          />
        </Tooltip>
        <Tooltip title={t('classGrading.tab.template.action.importTemplate')}>
          <Button
            className='padding0'
            type='link'
            icon={<Iconfont type='icon-daorudaochu' />}
            onClick={() => { onHandleMoreOperations('importTemplate') }}
          />
        </Tooltip>
      </Space>

      <DirectoryTree
        showIcon={false}
        height={treeHeight}
        className={styles.newTemplateTree}
        titleRender={titleRender}
        treeData={treeData}
        loadData={onLoadData}
        expandAction={false}
        selectedKeys={selectedTemplateNodeInfo?.key ? [selectedTemplateNodeInfo?.key] : undefined}
        onSelect={async (_key, { node }: any) => {
          await handleNodeSelect(_key, node);
        }}
        expandedKeys={[...expandedKeys]}
        onExpand={(expandedKeys) =>
          setExpandedKeys(expandedKeys)
        }
      ></DirectoryTree>
      {!rootLoading && !treeData?.length && (
        <div className={styles.treePlaceholder}>{t('db.auth.noElement')}</div>
      )}
    </Spin>
  );
});

export default NewTemplateTree;