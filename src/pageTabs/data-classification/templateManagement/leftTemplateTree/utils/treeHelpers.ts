import * as _ from "lodash";
import { getClassBuiltTempTagList } from "src/api/dataClassification";

// 树节点类型定义
export interface TreeNodeData {
  key: string;
  title: string;
  id: number;
  tp_id: number;
  parent_id: number;
  level_id?: number;
  label_ids?: string;
  label_names?: string;
  description?: string;
  isLeaf: boolean;
  hasChild: boolean;
  children?: TreeNodeData[];
  nodeType: string;
  classify_name: string;
  creator?: string;
  owner?: string;
  gmt_create?: string;
  gmt_modified?: string;
  isRoot?: boolean;
}

/**
 * 通用节点查找函数
 */
export const findNodeByKey = (nodes: any[], targetKey: any): any => {
  for (const node of nodes) {
    if (node.key === targetKey) {
      return node;
    }
    if (node.children) {
      const found = findNodeByKey(node.children, targetKey);
      if (found) return found;
    }
  }
  return null;
};

/**
 * 从缓存数据构建子树 - 避免重复API调用
 */
export const buildTreeFromCachedData = (allData: any[], tp_id: number, parent_id: number = 0): TreeNodeData[] => {
  return allData
    .filter((item: any) => item.parent_id === parent_id && item.tp_id === tp_id)
    .map((item: any) => {
      const hasChildren = allData.some((child: any) => child.parent_id === item.id);
      return {
        key: `classify_${item.id}`,
        title: item.classify_name,
        id: item.id,
        tp_id: item.tp_id,
        parent_id: item.parent_id,
        level_id: item.level_id,
        level_name: item.level_name,
        label_ids: item.label_ids,
        label_names: item.label_names,
        description: item.description,
        isLeaf: !hasChildren,
        hasChild: hasChildren,
        // 懒加载模式：有子节点时不预设children，让Tree组件触发loadData
        children: undefined,
        nodeType: 'classify',
        classify_name: item.classify_name,
        creator: item.creator,
        owner: item.owner,
        gmt_create: item.gmt_create,
        gmt_modified: item.gmt_modified
      };
    });
};

/**
 * 构建树结构的工具函数 - 懒加载模式（支持缓存）
 */
export const buildTreeFromResponseData = async (
  tp_id: number, 
  parent_id: number = 0, 
  cache?: Map<number, any[]>
): Promise<TreeNodeData[]> => {
  try {
    let allData: any[];

    // 检查缓存
    if (cache && cache.has(tp_id)) {
      console.log('使用缓存数据构建树结构, tp_id:', tp_id, 'parent_id:', parent_id);
      allData = cache.get(tp_id)!;
    } else {
      // 调用真实API获取标签数据
      console.log('调用API获取数据, tp_id:', tp_id);
      const response = await getClassBuiltTempTagList({
        action: 'query_list',
        tp_id: tp_id.toString(),
        limit: 1000
      });

      // 根据用户反馈，API直接返回datas数组
      allData = (response as any).datas || response || [];
      
      // 缓存数据
      if (cache) {
        cache.set(tp_id, allData);
        console.log('缓存数据, tp_id:', tp_id, '数据条数:', allData.length);
      }
    }

    return buildTreeFromCachedData(allData, tp_id, parent_id);
  } catch (error) {
    console.error('获取标签数据失败:', error);
    return [];
  }
};

/**
 * 递归获取指定节点下的所有子孙节点
 */
export const getAllDescendants = async (nodeId: number, tp_id: number, cache?: Map<string, any[]>): Promise<any[]> => {
  const cacheKey = `${tp_id}_${nodeId}`;
    console.log('testewst')

  // 检查缓存
  if (cache && cache.has(cacheKey)) {
    return cache.get(cacheKey)!;
  }

  try {
    // 调用真实API获取标签数据
    const response = await getClassBuiltTempTagList({
      action: 'query_list',
      tp_id: tp_id.toString(),
      limit: 1000
    });

    console.log(response)
    // 根据用户反馈，API直接返回datas数组
    const allData = (response as any).datas || response || [];
    const result: any[] = [];

    // 递归查询函数
    const collectDescendants = (parentId: number, currentTpId: number) => {
      const children = allData.filter((item: any) =>
        item.parent_id === parentId && item.tp_id === currentTpId
      );

      for (const child of children) {
        // 只收集有label_names的数据
        if (child.label_names && child.label_names.trim() !== '') {
          result.push(child);
          console.log('收集节点:', child.classify_name, 'label_names:', child.label_names);
        } else {
          console.log('跳过节点:', child.classify_name, 'label_names为空:', child.label_names);
        }

        // 第三层逻辑：如果label_ids不为空，说明到了最后一层，不再递归
        if (child.label_ids && child.label_ids.trim() !== '') {
          continue;
        }

        // 第二层逻辑：检查是否还有子节点，如果有则继续递归
        const hasChildren = allData.some((subChild: any) =>
          subChild.parent_id === child.id && subChild.tp_id === currentTpId
        );

        if (hasChildren) {
          collectDescendants(child.id, currentTpId);
        }
      }
    };

    // 开始递归收集
    collectDescendants(nodeId, tp_id);

    // 缓存结果
    if (cache) {
      cache.set(cacheKey, result);
    }

    return result;
  } catch (error) {
    console.error('获取子孙节点数据失败:', error);
    return [];
  }
};

/**
 * 重新构建展开节点的数据
 */
export const rebuildExpandedNodesData = async (
  expandedKeys: any[], 
  treeData: any[], 
  treeNodeChildrenMap: any,
  cache?: Map<number, any[]>
): Promise<{ 
  newTreeData: any[], 
  newTreeNodeChildrenMap: any 
}> => {
  console.log('重新构建展开节点数据:', expandedKeys);
  
  const cloneTreeData = _.cloneDeep(treeData);
  const cloneTreeNodeChildrenMap = _.cloneDeep(treeNodeChildrenMap);
  
  // 为每个展开的节点重新构建子数据
  for (const expandedKey of expandedKeys) {
    const targetNode = findNodeByKey(cloneTreeData, expandedKey);
    
    if (targetNode) {
      console.log('重新构建节点:', targetNode.title || targetNode.key);
      
      try {
        let children: any[] = [];
        
        // 根据节点类型重新获取子数据
        if (targetNode.isRoot && targetNode.tp_id) {
          // 模板节点，获取分类数据
          children = await buildTreeFromResponseData(targetNode.tp_id, 0, cache);
        } else if (targetNode.nodeType === 'classify' && targetNode.id && targetNode.tp_id) {
          // 分类节点，获取子分类数据
          children = await buildTreeFromResponseData(targetNode.tp_id, targetNode.id, cache);
        }
        
        // 更新节点的子数据
        if (children.length > 0) {
          targetNode.children = children;
          cloneTreeNodeChildrenMap[expandedKey] = children;
          console.log(`节点 ${targetNode.title} 重新构建完成，子节点数量:`, children.length);
        }
      } catch (error) {
        console.error('重新构建节点数据失败:', expandedKey, error);
      }
    }
  }
  
  console.log('所有展开节点数据重新构建完成');
  
  return {
    newTreeData: cloneTreeData,
    newTreeNodeChildrenMap: cloneTreeNodeChildrenMap
  };
};

/**
 * 在树数据和treeNodeChildrenMap中递归查找节点
 */
export const findNodeInTreeWithChildren = (
  nodes: any[], 
  targetKey: string, 
  treeNodeChildrenMap: any
): any => {
  for (const node of nodes) {
    console.log('查找节点:', node.key, '目标:', targetKey);
    if (node.key === targetKey) {
      return node;
    }

    // 先检查node.children
    if (node.children) {
      const found = findNodeInTreeWithChildren(node.children, targetKey, treeNodeChildrenMap);
      if (found) return found;
    }

    // 再检查treeNodeChildrenMap中的子节点
    const childrenFromMap = treeNodeChildrenMap[node.key];
    if (childrenFromMap && childrenFromMap.length > 0) {
      const found = findNodeInTreeWithChildren(childrenFromMap, targetKey, treeNodeChildrenMap);
      if (found) return found;
    }
  }
  return null;
};