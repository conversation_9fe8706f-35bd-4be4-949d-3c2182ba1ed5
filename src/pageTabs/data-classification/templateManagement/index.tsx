import React, { useState } from "react";
import classnames from 'classnames';
import { ResizableBox, ResizableProps } from "react-resizable";
import { Tabs, message } from 'antd';
import { ErrorBoundary, Iconfont } from 'src/components';
import RightTemplateContent from './RightTemplateContent';
import { useSelector, useDispatch } from 'src/hook';
import { TEMPLATE_TABS } from '../constants';
import { TemplateTab } from '../types';
import { setActiveTemplateTab } from '../dataClassificationSlice'
import BuiltInTemplateTree from './leftTemplateTree/BuiltInTemplateTree';
import NewTemplateTree from './leftTemplateTree/NewTemplateTree';
import {
	AddOrEditTemplateModal,
	ImportTemplateModa,
	GradeConfigModal,
	AddOrEditClassificationModal
} from './modals';
import {
	getClassNewTemplateAPI,
	getClassAddCategory,
	getClassDelCategory,
	getClassEditCategory,
	getClassAddClassification,
	getClassEditClassification,
	getClassDelClassification
} from 'src/api'
import styles from './index.module.scss';
import { useTranslation } from "react-i18next";

const ResizableBoxProps: ResizableProps = {
	axis: "x",
	width: 320,
	height: 0,
	minConstraints: [260, 0],
	maxConstraints: [600, 0],
};

const TemplateManagement = () => {

	const dispatch = useDispatch();
	const { t } = useTranslation();
	const { activeTemplateTab, selectedTemplateNodeInfo } = useSelector(state => state.dataClassification);
	//刷新树节点 - 内置模板
	const [needRefreshTree, setNeedRefreshTree] = useState<boolean>(false);
	//刷新树节点 - 新增模板
	const [needRefreshNewTree, setNeedRefreshNewTree] = useState<boolean>(false);
	//缓存清理信号
	const [clearCacheSignal, setClearCacheSignal] = useState<number>(0);
	//树更新信息
	const [treeUpdateInfo, setTreeUpdateInfo] = useState<{
		type: 'add' | 'edit' | 'delete' | 'refresh';
		nodeId?: string;
		parentId?: string;
		data?: any;
	} | null>(null);
	//编辑信息
	const [addOrEditTemplateModalVisible, setAddOrEditModalVisible] = useState<boolean>(false);
	//通用 当前编辑节点信息
	const [curEditingDataInfo, setCurEditingDataInfo] = useState<any>(null);
	//导入模板
	const [importTemplateModalVisible, setImportTemplateModalVisible] = useState<boolean>(false);
	//等级配置
	const [gradeConfigModalVisible, setGradeConfigModalVisible] = useState<boolean>(false);
	//新增 编辑分类 
	const [addClassModalVisible, setAddClassModalVisible] = useState<boolean>(false);

	const ResizeHandle = (
		<div className={styles.resizeHandle}>
			<Iconfont type="icon-handle-8"></Iconfont>
		</div>
	);


	//新增分类

	const onSubmitCategory = (params: any) => {
		// 使用新的API接口
		let action = getClassAddClassification;
		if (curEditingDataInfo) {
			action = getClassEditClassification; // 使用新的编辑分类接口
		}
		action(params).then(() => {
			// 只有API调用成功时才更新UI和树结构
			message.success(curEditingDataInfo ? t('common.message.editSuccess') : t('common.message.addSuccess'))
			setAddClassModalVisible(false);

			// 智能更新树结构 - 只在API成功时执行
			if (curEditingDataInfo) {
				// 编辑：更新对应节点
				setTreeUpdateInfo({
					type: 'edit',
					nodeId: curEditingDataInfo.key,
					data: { ...curEditingDataInfo, title: params.classify_name, classify_name: params.classify_name }
				});
			} else {
				// 新增：在当前选中节点下添加新节点
				setTreeUpdateInfo({
					type: 'add',
					parentId: selectedTemplateNodeInfo?.key,
					data: {
						key: `classify_${Date.now()}`, // 临时key，实际应该从API返回
						title: params.classify_name,
						classify_name: params.classify_name,
						id: params.id,
						tp_id: params.tp_id,
						level_id: params.level_id,
						parent_id: params.parent_id,
						nodeType: 'classify',
						isLeaf: true,
						hasChild: false
					}
				});
			}

			setCurEditingDataInfo(null);
		}).catch((error) => {
			// 不关闭Modal，让用户可以重试或取消
			// setAddClassModalVisible(false); // 注释掉，保持Modal打开
			// setCurEditingDataInfo(null); // 注释掉，保持编辑状态
		})
	}
	const onAddOrEditClass = (node?: any) => {
		setAddClassModalVisible(true)
		if (node) {
			setCurEditingDataInfo(node);
		}
	}

	//新增模板
	const onSubmitAddOrEditTemplate = (params: any) => {
		getClassNewTemplateAPI({ ...params }).then(() => {
			// 只有API调用成功时才更新UI
			if (curEditingDataInfo) {
				message.success(t('common.message.editSuccess'))
				// 编辑模板：更新对应节点
				setTreeUpdateInfo({
					type: 'edit',
					nodeId: curEditingDataInfo.key,
					data: { ...curEditingDataInfo, tp_name: params.tp_name, title: params.tp_name }
				});
			} else {
				message.success(t('common.message.addSuccess'))
				// 新增模板：刷新整个树
				setNeedRefreshTree(!needRefreshTree);
			}
			setAddOrEditModalVisible(false);
			setCurEditingDataInfo(null);
		}).catch((error) => {
		})
	}

	const onAddOrEditTemplate = (node?: any) => {
		setAddOrEditModalVisible(true);
		if (node) {
			setCurEditingDataInfo(node);
		}
	}

	const onDeleteTreeNode = (params: any) => {
		//删除模板
		if (selectedTemplateNodeInfo?.isRoot) {
			// 模板删除：使用 /api/template/manage 接口
			const deleteParams = {
				action: 'delete_template',
				id: String(selectedTemplateNodeInfo?.tp_id || params.id || '')
			};
			getClassNewTemplateAPI(deleteParams).then(() => {
				// 只有API调用成功时才更新树结构
				message.success(t('common.message.delete_success'));
				// 删除模板：从树中移除对应节点
				setTreeUpdateInfo({
					type: 'delete',
					nodeId: selectedTemplateNodeInfo?.key
				});
			}).catch((error) => {
			})
		}else {
			// 分类删除：使用 /api/del_classification 接口
			const deleteParams = {
				id: String(selectedTemplateNodeInfo?.id || params.id || '')
			};
			getClassDelClassification(deleteParams).then(() => {
				// 只有API调用成功时才更新树结构
				message.success(t('common.message.delete_success'));
				// 删除分类：从树中移除对应节点
				setTreeUpdateInfo({
					type: 'delete',
					nodeId: selectedTemplateNodeInfo?.key
				});
			}).catch((error) => {
				// API调用失败时，不更新树结构，保持原有状态
				// 树结构保持不变，用户可以重试删除操作
			})
		}
	}

	//等级配置
	const onSettingGradeConfg = (node?: any) => {
		setGradeConfigModalVisible(true)
	}

	// 刷新当前选中的树数据，保持展开状态和选中状态
	const refreshCurrentTree = () => {

		// 触发缓存清理 - 通过设置一个特殊的状态来通知树组件清空缓存
		setClearCacheSignal(Date.now());

		if (activeTemplateTab === 'newTemplate') {
			// 新增模板：使用独立的刷新变量
			setNeedRefreshNewTree(!needRefreshNewTree);

		} else if (activeTemplateTab === 'builtInTemplate') {
			// 内置模板：使用原有的刷新变量
			setNeedRefreshTree(!needRefreshTree);
		}
	}

	const onImportTemplate = () => {
		setImportTemplateModalVisible(true);
	}

	return (
		<div className={styles.templateManagement}>
			<ResizableBox
				className={styles.resizableBox}
				handle={ResizeHandle}
				{...ResizableBoxProps}
			>
				<div className={classnames(styles.leftWrap)}>
					<ErrorBoundary>
						<div className={styles.leftContainer}>
							<h4 className={styles.title}>{t('classGrading.tab.task.column.template')}</h4>
							<Tabs
								activeKey={activeTemplateTab}
								onChange={(key) => {
									dispatch(setActiveTemplateTab(key as TemplateTab));
								}}
							>
								{
									Object.keys(TEMPLATE_TABS).map((key) => (
										<Tabs.TabPane tab={TEMPLATE_TABS[key as TemplateTab]} key={key} />
									))
								}
							</Tabs>
							{/* 内置模板 */}
							{
								activeTemplateTab === 'builtInTemplate' &&
								<BuiltInTemplateTree
									needRefreshTree={needRefreshTree}
									clearCacheSignal={clearCacheSignal}
								/>
							}
							{/* 新增模板 */}
							{
								activeTemplateTab === 'newTemplate' &&
								<NewTemplateTree
									needRefreshTree={needRefreshNewTree}
									clearCacheSignal={clearCacheSignal}
									treeUpdateInfo={treeUpdateInfo}
									onTreeUpdateComplete={() => setTreeUpdateInfo(null)}
									onAddOrEditClass={onAddOrEditClass}
									onAddOrEditTemplate={onAddOrEditTemplate}
									onDeleteTreeNode={onDeleteTreeNode}
									onSettingGradeConfg={onSettingGradeConfg}
									onImportTemplate={onImportTemplate}
								/>
							}
						</div>
					</ErrorBoundary>
				</div>
			</ResizableBox>
			<div className={styles.rightWrap}>
				<ErrorBoundary>
					<RightTemplateContent
						setGradeConfigModalVisible={setGradeConfigModalVisible}
						onRefreshTree={refreshCurrentTree}
					/>
				</ErrorBoundary>
			</div>
			{/* 新建 编辑 弹框 */}
			{
				addOrEditTemplateModalVisible &&
				<AddOrEditTemplateModal
					detailInfo={curEditingDataInfo}
					onCancel={() => { setAddOrEditModalVisible(false); setCurEditingDataInfo(null); }}
					onSubmit={(params: any) => onSubmitAddOrEditTemplate(params)}
				/>
			}
			{/* 导入模板 */}
			{
				importTemplateModalVisible &&
				<ImportTemplateModa
					onCancel={() => { setImportTemplateModalVisible(false); }}
				/>
			}
			{/* 等级配置 */}
			{
				gradeConfigModalVisible &&
				<GradeConfigModal
					onCancel={() => { setGradeConfigModalVisible(false); }}
					onRefreshTree={refreshCurrentTree}
				/>
			}
			{/* 新增 编辑分类 */}
			{
				addClassModalVisible &&
				<AddOrEditClassificationModal
					detailInfo={curEditingDataInfo}
					onCancel={() => { setAddClassModalVisible(false); setCurEditingDataInfo(null) }}
				  onSubmit={onSubmitCategory}
				/>
			}
		</div>
	)
}

export default TemplateManagement;